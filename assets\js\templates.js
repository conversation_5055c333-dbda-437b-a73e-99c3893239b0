/**
 * Database App Builder - Templates JavaScript
 *
 * Handles template browsing, preview, and installation
 */

jQuery(document).ready(function($) {
    'use strict';

    let currentTemplateId = null;

    // Debug: Check if variables are loaded
    console.log('DAB Templates JS loaded');
    console.log('dab_templates object:', typeof dab_templates !== 'undefined' ? dab_templates : 'undefined');

    // Check if dab_templates is available
    if (typeof dab_templates === 'undefined') {
        console.error('DAB Templates: dab_templates object is not defined. Scripts may not be properly localized.');
        return;
    }

    // Check if required elements exist
    console.log('Preview modal exists:', $('#dab-template-preview-modal').length > 0);
    console.log('Modal overlay exists:', $('.dab-modal-overlay').length > 0);
    console.log('Template grid exists:', $('#templates-grid').length > 0);
    console.log('Template cards found:', $('.dab-template-card').length);

    // Verify AJAX URL and nonce
    console.log('AJAX URL:', dab_templates.ajax_url);
    console.log('Nonce:', dab_templates.nonce);

    // Test if jQuery is working
    console.log('jQuery version:', $.fn.jquery);

    // Add debug function for troubleshooting
    window.dabTemplatesDebug = function() {
        console.log('=== DAB Templates Debug Info ===');
        console.log('dab_templates object:', dab_templates);
        console.log('Template cards:', $('.dab-template-card').length);
        console.log('Preview buttons:', $('.dab-preview-template').length);
        console.log('Install buttons:', $('.dab-install-template').length);
        console.log('Preview modal:', $('#dab-template-preview-modal').length);
        console.log('Install modal:', $('#dab-template-install-modal').length);
        console.log('Modal overlay:', $('.dab-modal-overlay').length);
        console.log('Current template ID:', currentTemplateId);

        // Test button click handlers
        $('.dab-preview-template').each(function(index) {
            console.log('Preview button ' + index + ':', {
                templateId: $(this).data('template-id'),
                hasClickHandler: $._data(this, 'events') ? 'Yes' : 'No'
            });
        });

        $('.dab-install-template').each(function(index) {
            console.log('Install button ' + index + ':', {
                templateId: $(this).data('template-id'),
                templateName: $(this).data('template-name'),
                hasClickHandler: $._data(this, 'events') ? 'Yes' : 'No'
            });
        });
        console.log('=== End Debug Info ===');
    };

    // Template search functionality
    $('#search-templates').on('click', function() {
        searchTemplates();
    });

    $('#template-search').on('keypress', function(e) {
        if (e.which === 13) {
            searchTemplates();
        }
    });

    $('#template-category').on('change', function() {
        searchTemplates();
    });

    // Template preview
    $(document).on('click', '.dab-preview-template', function(e) {
        e.preventDefault();
        console.log('Preview button clicked');

        const templateId = $(this).data('template-id');
        console.log('Template ID for preview:', templateId);

        if (!templateId) {
            console.error('No template ID found for preview');
            showNotice('error', 'Template ID not found');
            return;
        }

        previewTemplate(templateId);
    });

    // Template installation
    $(document).on('click', '.dab-install-template', function(e) {
        e.preventDefault();
        console.log('Install button clicked');

        const templateId = $(this).data('template-id');
        const templateName = $(this).data('template-name');
        console.log('Template ID for install:', templateId);
        console.log('Template name for install:', templateName);

        if (!templateId) {
            console.error('No template ID found for installation');
            showNotice('error', 'Template ID not found');
            return;
        }

        if (!templateName) {
            console.error('No template name found for installation');
            showNotice('error', 'Template name not found');
            return;
        }

        showInstallModal(templateId, templateName);
    });

    // Install from preview
    $('#dab-install-from-preview').on('click', function() {
        if (currentTemplateId) {
            $('#dab-template-preview-modal').hide();
            const templateName = $('#dab-preview-title').text().replace('Template Preview - ', '');
            showInstallModal(currentTemplateId, templateName);
        }
    });

    // Confirm installation
    $('#dab-confirm-install').on('click', function() {
        console.log('Confirm install button clicked');

        const installationName = $('#installation-name').val().trim();
        console.log('Installation name:', installationName);

        if (!installationName) {
            const message = dab_templates.messages.installation_name_required || 'Please enter an installation name.';
            showNotice('error', message);
            return;
        }

        if (!currentTemplateId) {
            console.error('No template selected for installation');
            showNotice('error', 'No template selected.');
            return;
        }

        console.log('Starting installation for template ID:', currentTemplateId);
        installTemplate(currentTemplateId, installationName);
    });

    // Delete installation
    $(document).on('click', '.dab-delete-installation', function() {
        const installationId = $(this).data('installation-id');
        const installationName = $(this).data('installation-name');

        if (confirm(dab_templates.messages.confirm_delete.replace('%s', installationName))) {
            deleteInstallation(installationId);
        }
    });

    // Modal controls
    $(document).on('click', '.dab-modal-close', function() {
        console.log('Modal close button clicked');
        closeModals();
    });

    $(document).on('click', '.dab-modal-overlay', function() {
        console.log('Modal overlay clicked');
        closeModals();
    });

    // Close modal on escape key
    $(document).on('keydown', function(e) {
        if (e.keyCode === 27) { // Escape key
            closeModals();
        }
    });

    // Search templates function
    function searchTemplates() {
        const searchTerm = $('#template-search').val();
        const category = $('#template-category').val();

        $.ajax({
            url: dab_templates.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_search_templates',
                nonce: dab_templates.nonce,
                search: searchTerm,
                category: category
            },
            beforeSend: function() {
                $('#search-templates').addClass('loading');
            },
            success: function(response) {
                if (response.success) {
                    updateTemplateGrid(response.data.templates);
                } else {
                    showNotice('error', response.data || 'Search failed');
                }
            },
            error: function() {
                showNotice('error', 'Search request failed');
            },
            complete: function() {
                $('#search-templates').removeClass('loading');
            }
        });
    }

    // Update template grid
    function updateTemplateGrid(templates) {
        const grid = $('#templates-grid');
        grid.empty();

        if (templates.length === 0) {
            grid.html(`
                <div class="dab-no-templates">
                    <div class="dab-no-templates-icon">
                        <span class="dashicons dashicons-admin-appearance"></span>
                    </div>
                    <h3>No Templates Found</h3>
                    <p>No templates match your search criteria. Try adjusting your filters.</p>
                </div>
            `);
            return;
        }

        templates.forEach(function(template) {
            const templateCard = createTemplateCard(template);
            grid.append(templateCard);
        });
    }

    // Create template card HTML
    function createTemplateCard(template) {
        const installCount = template.install_count || 0;
        const rating = template.rating || 0;
        const icon = template.icon || 'dashicons-admin-generic';
        const category = template.category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        return `
            <div class="dab-template-card" data-category="${template.category}">
                <div class="dab-template-header">
                    <div class="dab-template-icon">
                        <span class="dashicons ${icon}"></span>
                    </div>
                    <div class="dab-template-meta">
                        <h3 class="dab-template-title">${template.name}</h3>
                        <span class="dab-template-category">${category}</span>
                    </div>
                </div>

                <div class="dab-template-body">
                    <p class="dab-template-description">${template.description}</p>

                    <div class="dab-template-stats">
                        <span class="dab-install-count">
                            <span class="dashicons dashicons-download"></span>
                            ${installCount} installs
                        </span>
                        ${rating > 0 ? `
                            <span class="dab-rating">
                                <span class="dashicons dashicons-star-filled"></span>
                                ${rating.toFixed(1)}
                            </span>
                        ` : ''}
                    </div>
                </div>

                <div class="dab-template-footer">
                    <button type="button" class="dab-btn dab-btn-outline-primary dab-preview-template"
                            data-template-id="${template.id}">
                        <span class="dashicons dashicons-visibility"></span>
                        Preview
                    </button>

                    <button type="button" class="dab-btn dab-btn-primary dab-install-template"
                            data-template-id="${template.id}"
                            data-template-name="${template.name}">
                        <span class="dashicons dashicons-download"></span>
                        Install
                    </button>
                </div>
            </div>
        `;
    }

    // Preview template
    function previewTemplate(templateId) {
        console.log('Preview template called with ID:', templateId);

        // Validate inputs
        if (!templateId) {
            console.error('Preview: No template ID provided');
            showNotice('error', 'Template ID is required for preview');
            return;
        }

        if (!dab_templates.ajax_url) {
            console.error('Preview: AJAX URL not available');
            showNotice('error', 'AJAX URL not configured');
            return;
        }

        if (!dab_templates.nonce) {
            console.error('Preview: Nonce not available');
            showNotice('error', 'Security nonce not configured');
            return;
        }

        console.log('AJAX URL:', dab_templates.ajax_url);
        console.log('Nonce:', dab_templates.nonce);

        $.ajax({
            url: dab_templates.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_get_template_preview',
                nonce: dab_templates.nonce,
                template_id: templateId
            },
            beforeSend: function() {
                console.log('Showing preview modal...');
                showModal('#dab-template-preview-modal');
                $('#dab-preview-content').html('<div class="dab-loading">Loading preview...</div>');
            },
            success: function(response) {
                console.log('Preview response:', response);

                if (response && response.success) {
                    currentTemplateId = templateId;

                    if (response.data && response.data.template && response.data.template.name) {
                        $('#dab-preview-title').text('Template Preview - ' + response.data.template.name);
                    } else {
                        $('#dab-preview-title').text('Template Preview');
                    }

                    if (response.data && response.data.preview_html) {
                        $('#dab-preview-content').html(response.data.preview_html);
                    } else {
                        $('#dab-preview-content').html('<div class="dab-error">No preview content available</div>');
                    }
                } else {
                    console.error('Preview failed:', response);
                    const errorMessage = (response && response.data) ? response.data : 'Unknown error occurred';
                    $('#dab-preview-content').html('<div class="dab-error">Failed to load preview: ' + errorMessage + '</div>');
                    showNotice('error', dab_templates.messages.preview_error || 'Failed to load template preview');
                }
            },
            error: function(xhr, status, error) {
                console.error('Preview AJAX error:', {xhr: xhr, status: status, error: error});
                console.error('Response text:', xhr.responseText);

                let errorMessage = 'Preview request failed';
                if (xhr.responseText) {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        if (errorResponse.data) {
                            errorMessage = errorResponse.data;
                        }
                    } catch (e) {
                        errorMessage = xhr.responseText.substring(0, 100) + '...';
                    }
                }

                $('#dab-preview-content').html('<div class="dab-error">' + errorMessage + '</div>');
                showNotice('error', dab_templates.messages.ajax_error || 'Request failed. Please try again.');
            },
            timeout: 30000 // 30 second timeout
        });
    }

    // Show install modal
    function showInstallModal(templateId, templateName) {
        currentTemplateId = templateId;
        $('#installation-name').val('');
        $('#installation-progress').hide();
        showModal('#dab-template-install-modal');

        // Suggest installation name based on template
        const suggestedName = templateName.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 20);
        $('#installation-name').attr('placeholder', 'e.g., ' + suggestedName);
    }

    // Install template
    function installTemplate(templateId, installationName) {
        console.log('Install template called with ID:', templateId, 'Name:', installationName);

        // Validate inputs
        if (!templateId) {
            console.error('Install: No template ID provided');
            showNotice('error', 'Template ID is required for installation');
            return;
        }

        if (!installationName) {
            console.error('Install: No installation name provided');
            showNotice('error', 'Installation name is required');
            return;
        }

        if (!dab_templates.ajax_url) {
            console.error('Install: AJAX URL not available');
            showNotice('error', 'AJAX URL not configured');
            return;
        }

        if (!dab_templates.nonce) {
            console.error('Install: Nonce not available');
            showNotice('error', 'Security nonce not configured');
            return;
        }

        $.ajax({
            url: dab_templates.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_install_template',
                nonce: dab_templates.nonce,
                template_id: templateId,
                installation_name: installationName
            },
            beforeSend: function() {
                console.log('Starting template installation...');
                $('#dab-confirm-install').prop('disabled', true).addClass('loading');
                $('#installation-progress').show();
                $('.dab-progress-text').text(dab_templates.messages.installing || 'Installing template...');
            },
            success: function(response) {
                console.log('Install response:', response);

                if (response && response.success) {
                    $('.dab-progress-text').text(dab_templates.messages.install_success || 'Template installed successfully!');

                    const successMessage = (response.data && response.data.message) ? response.data.message : 'Template installed successfully!';
                    showNotice('success', successMessage);

                    setTimeout(function() {
                        closeModals();
                        if (response.data && response.data.redirect_url) {
                            console.log('Redirecting to:', response.data.redirect_url);
                            window.location.href = response.data.redirect_url;
                        } else {
                            console.log('Reloading page...');
                            location.reload();
                        }
                    }, 2000);
                } else {
                    console.error('Installation failed:', response);
                    $('.dab-progress-text').text('Installation failed');

                    const errorMessage = (response && response.data) ? response.data : (dab_templates.messages.install_error || 'Installation failed');
                    showNotice('error', errorMessage);
                }
            },
            error: function(xhr, status, error) {
                console.error('Install AJAX error:', {xhr: xhr, status: status, error: error});
                console.error('Response text:', xhr.responseText);

                $('.dab-progress-text').text('Installation failed');

                let errorMessage = dab_templates.messages.install_error || 'Installation failed';
                if (xhr.responseText) {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        if (errorResponse.data) {
                            errorMessage = errorResponse.data;
                        }
                    } catch (e) {
                        // Keep default error message
                    }
                }

                showNotice('error', errorMessage);
            },
            complete: function() {
                console.log('Install request completed');
                $('#dab-confirm-install').prop('disabled', false).removeClass('loading');
            },
            timeout: 60000 // 60 second timeout for installation
        });
    }

    // Delete installation
    function deleteInstallation(installationId) {
        $.ajax({
            url: dab_templates.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_delete_template_installation',
                nonce: dab_templates.nonce,
                installation_id: installationId
            },
            beforeSend: function() {
                $(`.dab-delete-installation[data-installation-id="${installationId}"]`).addClass('loading');
            },
            success: function(response) {
                if (response.success) {
                    showNotice('success', response.data.message);
                    $(`.dab-delete-installation[data-installation-id="${installationId}"]`).closest('.dab-installed-card').fadeOut();
                } else {
                    showNotice('error', response.data || 'Failed to delete installation');
                }
            },
            error: function() {
                showNotice('error', 'Delete request failed');
            },
            complete: function() {
                $(`.dab-delete-installation[data-installation-id="${installationId}"]`).removeClass('loading');
            }
        });
    }

    // Show modal
    function showModal(modalSelector) {
        console.log('Showing modal:', modalSelector);

        // Check if modal exists
        if ($(modalSelector).length === 0) {
            console.error('Modal not found:', modalSelector);
            return;
        }

        // Check if overlay exists
        if ($('.dab-modal-overlay').length === 0) {
            console.error('Modal overlay not found');
            return;
        }

        $('.dab-modal-overlay').show();
        $(modalSelector).show();
        console.log('Modal shown successfully');
    }

    // Close modals
    function closeModals() {
        console.log('Closing modals');
        $('.dab-modal').hide();
        $('.dab-modal-overlay').hide();
        currentTemplateId = null;
    }

    // Show notice
    function showNotice(type, message) {
        const noticeClass = type === 'success' ? 'notice-success' : 'notice-error';
        const notice = $(`
            <div class="notice ${noticeClass} is-dismissible">
                <p>${message}</p>
                <button type="button" class="notice-dismiss">
                    <span class="screen-reader-text">Dismiss this notice.</span>
                </button>
            </div>
        `);

        $('.dab-admin-header').after(notice);

        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            notice.fadeOut();
        }, 5000);

        // Manual dismiss
        notice.find('.notice-dismiss').on('click', function() {
            notice.fadeOut();
        });
    }
});
