/**
 * Database App Builder - Templates Styles
 *
 * Styles for template browsing, preview, and installation
 */

/* Template Container */
.dab-templates-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Template Filters */
.dab-templates-filters {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.dab-filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 200px;
}

.dab-filter-group label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.dab-search-input,
.dab-filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.dab-search-input:focus,
.dab-filter-select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Template Grid */
.dab-templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

/* Template Card */
.dab-template-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dab-template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #0073aa;
}

.dab-template-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.dab-template-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #0073aa, #005177);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 24px;
}

.dab-template-meta {
    flex: 1;
}

.dab-template-title {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.dab-template-category {
    background: #f0f6fc;
    color: #0969da;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dab-template-body {
    padding: 20px;
}

.dab-template-description {
    color: #666;
    line-height: 1.5;
    margin: 0 0 15px 0;
    font-size: 14px;
}

.dab-template-stats {
    display: flex;
    gap: 15px;
    align-items: center;
    font-size: 13px;
    color: #777;
}

.dab-install-count,
.dab-rating {
    display: flex;
    align-items: center;
    gap: 4px;
}

.dab-rating .dashicons {
    color: #ffb900;
}

.dab-template-footer {
    padding: 15px 20px;
    background: #f9f9f9;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Ensure buttons are properly styled and clickable */
.dab-template-footer .dab-btn {
    position: relative;
    z-index: 10;
    pointer-events: auto;
    cursor: pointer;
    min-width: 100px;
    text-align: center;
}

.dab-template-footer .dab-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.dab-template-footer .dab-btn:active {
    transform: translateY(0);
}

.dab-template-footer .dab-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.dab-template-footer .dab-btn .dashicons {
    margin-right: 5px;
    vertical-align: middle;
}

/* No Templates */
.dab-no-templates {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    grid-column: 1 / -1;
}

.dab-no-templates-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.dab-no-templates h3 {
    margin: 0 0 10px 0;
    color: #333;
}

/* Installed Templates */
.dab-installed-templates {
    margin-top: 40px;
    padding-top: 40px;
    border-top: 2px solid #f0f0f0;
}

.dab-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 20px 0;
    font-size: 20px;
    color: #333;
}

.dab-section-title .dashicons {
    color: #00a32a;
}

.dab-installed-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.dab-installed-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    border-left: 4px solid #00a32a;
}

.dab-installed-header {
    padding: 15px 20px;
    background: #f9f9f9;
    border-bottom: 1px solid #f0f0f0;
}

.dab-installed-header h4 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 16px;
}

.dab-installed-date {
    font-size: 12px;
    color: #777;
}

.dab-installed-body {
    padding: 15px 20px;
}

.dab-installation-summary {
    display: flex;
    gap: 15px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.dab-summary-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #666;
    background: #f0f0f0;
    padding: 4px 8px;
    border-radius: 12px;
}

.dab-installed-footer {
    padding: 15px 20px;
    background: #f9f9f9;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Modal Styles */
.dab-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 100001;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
}

.dab-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
}

.dab-modal-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.dab-modal-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f9f9f9;
}

.dab-modal-header h3 {
    margin: 0;
    color: #333;
}

.dab-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.dab-modal-close:hover {
    background: #f0f0f0;
    color: #333;
}

.dab-modal-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.dab-modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    background: #f9f9f9;
}

/* Template Preview */
.dab-template-preview h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.dab-template-preview h4 {
    margin: 20px 0 10px 0;
    color: #0073aa;
    font-size: 16px;
}

.dab-preview-list {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

.dab-preview-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-field-count {
    color: #666;
    font-size: 12px;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 10px;
}

/* Preview Loading and Error States */
.dab-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.dab-loading:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dab-error {
    text-align: center;
    padding: 20px;
    color: #d63638;
    background: #fef7f7;
    border: 1px solid #f1adad;
    border-radius: 4px;
    margin: 10px 0;
}

.dab-preview-error {
    text-align: center;
    padding: 20px;
    color: #666;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin: 10px 0;
}

.dab-template-info {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

/* Installation Progress */
.dab-installation-progress {
    margin-top: 20px;
}

.dab-progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.dab-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005177);
    width: 0%;
    animation: progress-animation 2s ease-in-out infinite;
}

@keyframes progress-animation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.dab-progress-text {
    text-align: center;
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* Form Styles */
.dab-form-group {
    margin-bottom: 20px;
}

.dab-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.dab-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.dab-form-control:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.dab-form-help {
    margin: 5px 0 0 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dab-templates-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .dab-filter-group {
        min-width: auto;
    }

    .dab-templates-grid {
        grid-template-columns: 1fr;
    }

    .dab-template-footer {
        flex-direction: column;
    }

    .dab-modal {
        width: 95%;
        max-height: 90vh;
    }

    .dab-installed-grid {
        grid-template-columns: 1fr;
    }
}
